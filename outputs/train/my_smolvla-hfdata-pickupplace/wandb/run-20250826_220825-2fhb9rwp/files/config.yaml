_wandb:
    value:
        cli_version: 0.21.1
        e:
            fp3fosiehlujs4fbpx8ntrdqlh16j2y9:
                args:
                    - --policy.path=lerobot/smolvla_base
                    - --dataset.repo_id=lerobot/svla_so100_pickplace
                    - --batch_size=64
                    - --steps=20000
                    - --output_dir=outputs/train/my_smolvla
                    - --job_name=my_smolvla_training
                    - --policy.device=cuda
                    - --wandb.enable=true
                    - --policy.push_to_hub=false
                cpu_count: 24
                cpu_count_logical: 24
                cudaVersion: "12.4"
                disk:
                    /:
                        total: "************"
                        used: "************"
                executable: /mnt/vdb1/envs/lerobot/bin/python3.10
                gpu: NVIDIA H800
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H800
                      uuid: GPU-8f6a2715-cf0e-6220-3ad7-70072cbe7901
                host: VM-59-6-ubuntu
                memory:
                    total: "100706693120"
                os: Linux-5.4.0-72-generic-x86_64-with-glibc2.35
                program: /mnt/vdb1/envs/lerobot/bin/lerobot-train
                python: CPython 3.10.18
                root: outputs/train/my_smolvla
                startedAt: "2025-08-26T14:08:25.654087Z"
                writerId: fp3fosiehlujs4fbpx8ntrdqlh16j2y9
        m: []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 41
                - 49
                - 51
            "2":
                - 1
                - 11
                - 41
                - 49
                - 51
                - 71
            "3":
                - 13
                - 15
                - 16
                - 61
            "4": 3.10.18
            "5": 0.21.1
            "10":
                - 21
            "12": 0.21.1
            "13": linux-x86_64
batch_size:
    value: 64
dataset:
    value:
        episodes: null
        image_transforms:
            enable: false
            max_num_transforms: 3
            random_order: false
            tfs:
                brightness:
                    kwargs:
                        brightness:
                            - 0.8
                            - 1.2
                    type: ColorJitter
                    weight: 1
                contrast:
                    kwargs:
                        contrast:
                            - 0.8
                            - 1.2
                    type: ColorJitter
                    weight: 1
                hue:
                    kwargs:
                        hue:
                            - -0.05
                            - 0.05
                    type: ColorJitter
                    weight: 1
                saturation:
                    kwargs:
                        saturation:
                            - 0.5
                            - 1.5
                    type: ColorJitter
                    weight: 1
                sharpness:
                    kwargs:
                        sharpness:
                            - 0.5
                            - 1.5
                    type: SharpnessJitter
                    weight: 1
        repo_id: lerobot/svla_so100_pickplace
        revision: null
        root: null
        use_imagenet_stats: true
        video_backend: torchcodec
env:
    value: null
eval:
    value:
        batch_size: 50
        n_episodes: 50
        use_async_envs: false
eval_freq:
    value: 20000
job_name:
    value: my_smolvla_training
log_freq:
    value: 200
num_workers:
    value: 4
optimizer:
    value:
        betas:
            - 0.9
            - 0.95
        eps: 1e-08
        grad_clip_norm: 10
        lr: 0.0001
        type: adamw
        weight_decay: 1e-10
output_dir:
    value: outputs/train/my_smolvla
policy:
    value:
        adapt_to_pi_aloha: false
        add_image_special_tokens: false
        attention_mode: cross_attn
        chunk_size: 50
        device: cuda
        empty_cameras: 0
        expert_width_multiplier: 0.75
        freeze_vision_encoder: true
        input_features:
            observation.image:
                shape:
                    - 3
                    - 256
                    - 256
                type: VISUAL
            observation.image2:
                shape:
                    - 3
                    - 256
                    - 256
                type: VISUAL
            observation.image3:
                shape:
                    - 3
                    - 256
                    - 256
                type: VISUAL
            observation.state:
                shape:
                    - 6
                type: STATE
        license: null
        load_vlm_weights: true
        max_action_dim: 32
        max_period: 4
        max_state_dim: 32
        min_period: 0.004
        n_action_steps: 50
        n_obs_steps: 1
        normalization_mapping:
            ACTION: MEAN_STD
            STATE: MEAN_STD
            VISUAL: IDENTITY
        num_expert_layers: 0
        num_steps: 10
        num_vlm_layers: 16
        optimizer_betas:
            - 0.9
            - 0.95
        optimizer_eps: 1e-08
        optimizer_grad_clip_norm: 10
        optimizer_lr: 0.0001
        optimizer_weight_decay: 1e-10
        output_features:
            action:
                shape:
                    - 6
                type: ACTION
        pad_language_to: max_length
        prefix_length: 0
        private: null
        push_to_hub: false
        repo_id: null
        resize_imgs_with_padding:
            - 512
            - 512
        scheduler_decay_lr: 2.5e-06
        scheduler_decay_steps: 30000
        scheduler_warmup_steps: 1000
        self_attn_every_n_layers: 2
        tags: null
        tokenizer_max_length: 48
        train_expert_only: true
        train_state_proj: true
        type: smolvla
        use_amp: false
        use_cache: true
        use_delta_joint_actions_aloha: false
        vlm_model_name: HuggingFaceTB/SmolVLM2-500M-Video-Instruct
resume:
    value: false
save_checkpoint:
    value: true
save_freq:
    value: 20000
scheduler:
    value:
        decay_lr: 2.5e-06
        num_decay_steps: 30000
        num_warmup_steps: 1000
        peak_lr: 0.0001
        type: cosine_decay_with_warmup
seed:
    value: 1000
steps:
    value: 20000
use_policy_training_preset:
    value: true
wandb:
    value:
        disable_artifact: false
        enable: true
        entity: null
        mode: null
        notes: null
        project: lerobot
        run_id: null
