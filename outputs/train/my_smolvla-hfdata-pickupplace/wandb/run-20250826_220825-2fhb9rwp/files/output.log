[1m[34mLogs will be synced with wandb.[0m
INFO 2025-08-26 22:08:27 db_utils.py:103 Track this run --> [1m[33mhttps://wandb.ai/xxll/lerobot/runs/2fhb9rwp[0m
INFO 2025-08-26 22:08:27 ts/train.py:127 Creating dataset
Resolving data files: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████| 50/50 [00:00<00:00, 52547.03it/s]
INFO 2025-08-26 22:08:28 ts/train.py:138 Creating policy
Loading  HuggingFaceTB/SmolVLM2-500M-Video-Instruct weights ...
INFO 2025-08-26 22:08:34 odeling.py:1004 We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Reducing the number of VLM layers to 16 ...
[standardise_state_dict] 'normalize_inputs.buffer_observation_state.mean'  ←  ['normalize_inputs.so100-red_buffer_observation_state.mean', 'normalize_inputs.so100_buffer_observation_state.mean']
[standardise_state_dict] 'normalize_inputs.buffer_observation_state.std'  ←  ['normalize_inputs.so100-red_buffer_observation_state.std', 'normalize_inputs.so100_buffer_observation_state.std']
[standardise_state_dict] 'normalize_targets.buffer_action.mean'  ←  ['normalize_targets.so100-red_buffer_action.mean', 'normalize_targets.so100_buffer_action.mean']
[standardise_state_dict] 'normalize_targets.buffer_action.std'  ←  ['normalize_targets.so100-red_buffer_action.std', 'normalize_targets.so100_buffer_action.std']
[standardise_state_dict] 'unnormalize_outputs.buffer_action.mean'  ←  ['unnormalize_outputs.so100-red_buffer_action.mean', 'unnormalize_outputs.so100_buffer_action.mean']
[standardise_state_dict] 'unnormalize_outputs.buffer_action.std'  ←  ['unnormalize_outputs.so100-red_buffer_action.std', 'unnormalize_outputs.so100_buffer_action.std']
INFO 2025-08-26 22:08:45 ts/train.py:144 Creating optimizer and scheduler
INFO 2025-08-26 22:08:45 ts/train.py:156 [1m[33mOutput dir:[0m outputs/train/my_smolvla
INFO 2025-08-26 22:08:45 ts/train.py:159 cfg.steps=20000 (20K)
INFO 2025-08-26 22:08:45 ts/train.py:160 dataset.num_frames=19631 (20K)
INFO 2025-08-26 22:08:45 ts/train.py:161 dataset.num_episodes=50
INFO 2025-08-26 22:08:45 ts/train.py:162 num_learnable_params=99880992 (100M)
INFO 2025-08-26 22:08:45 ts/train.py:163 num_total_params=450046212 (450M)
INFO 2025-08-26 22:08:45 ts/train.py:202 Start offline training on a fixed dataset
INFO 2025-08-26 22:11:01 ts/train.py:232 step:200 smpl:13K ep:33 epch:0.65 loss:0.049 grdn:0.439 lr:1.0e-05 updt_s:0.361 data_s:0.314
WARNING 2025-08-26 22:11:01 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:11:01 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:13:22 ts/train.py:232 step:400 smpl:26K ep:65 epch:1.30 loss:0.030 grdn:0.356 lr:3.0e-05 updt_s:0.356 data_s:0.347
WARNING 2025-08-26 22:13:22 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:13:22 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:15:43 ts/train.py:232 step:600 smpl:38K ep:98 epch:1.96 loss:0.027 grdn:0.374 lr:5.0e-05 updt_s:0.354 data_s:0.353
WARNING 2025-08-26 22:15:44 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:15:44 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:18:17 ts/train.py:232 step:800 smpl:51K ep:130 epch:2.61 loss:0.028 grdn:0.410 lr:7.0e-05 updt_s:0.353 data_s:0.411
WARNING 2025-08-26 22:18:17 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:18:17 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:20:48 ts/train.py:232 step:1K smpl:64K ep:163 epch:3.26 loss:0.029 grdn:0.411 lr:9.0e-05 updt_s:0.354 data_s:0.401
WARNING 2025-08-26 22:20:48 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:20:48 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:23:12 ts/train.py:232 step:1K smpl:77K ep:196 epch:3.91 loss:0.027 grdn:0.386 lr:1.0e-04 updt_s:0.354 data_s:0.363
WARNING 2025-08-26 22:23:12 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:23:12 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:25:40 ts/train.py:232 step:1K smpl:90K ep:228 epch:4.56 loss:0.027 grdn:0.378 lr:1.0e-04 updt_s:0.355 data_s:0.381
WARNING 2025-08-26 22:25:40 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:25:40 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:28:09 ts/train.py:232 step:2K smpl:102K ep:261 epch:5.22 loss:0.025 grdn:0.353 lr:9.9e-05 updt_s:0.353 data_s:0.392
WARNING 2025-08-26 22:28:09 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:28:09 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:30:40 ts/train.py:232 step:2K smpl:115K ep:293 epch:5.87 loss:0.024 grdn:0.332 lr:9.9e-05 updt_s:0.354 data_s:0.396
WARNING 2025-08-26 22:30:40 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:30:40 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:33:06 ts/train.py:232 step:2K smpl:128K ep:326 epch:6.52 loss:0.023 grdn:0.316 lr:9.9e-05 updt_s:0.354 data_s:0.373
WARNING 2025-08-26 22:33:06 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:33:06 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:35:31 ts/train.py:232 step:2K smpl:141K ep:359 epch:7.17 loss:0.022 grdn:0.312 lr:9.9e-05 updt_s:0.353 data_s:0.372
WARNING 2025-08-26 22:35:32 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:35:32 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:38:03 ts/train.py:232 step:2K smpl:154K ep:391 epch:7.82 loss:0.021 grdn:0.293 lr:9.9e-05 updt_s:0.354 data_s:0.399
WARNING 2025-08-26 22:38:03 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:38:03 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:40:29 ts/train.py:232 step:3K smpl:166K ep:424 epch:8.48 loss:0.021 grdn:0.293 lr:9.8e-05 updt_s:0.353 data_s:0.377
WARNING 2025-08-26 22:40:29 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:40:29 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:42:55 ts/train.py:232 step:3K smpl:179K ep:456 epch:9.13 loss:0.020 grdn:0.284 lr:9.8e-05 updt_s:0.352 data_s:0.377
WARNING 2025-08-26 22:42:55 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:42:55 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:45:16 ts/train.py:232 step:3K smpl:192K ep:489 epch:9.78 loss:0.020 grdn:0.276 lr:9.8e-05 updt_s:0.355 data_s:0.344
WARNING 2025-08-26 22:45:16 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:45:16 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:47:40 ts/train.py:232 step:3K smpl:205K ep:522 epch:10.43 loss:0.019 grdn:0.270 lr:9.7e-05 updt_s:0.353 data_s:0.365
WARNING 2025-08-26 22:47:40 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:47:40 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:50:04 ts/train.py:232 step:3K smpl:218K ep:554 epch:11.08 loss:0.019 grdn:0.272 lr:9.7e-05 updt_s:0.354 data_s:0.366
WARNING 2025-08-26 22:50:04 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:50:04 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:52:35 ts/train.py:232 step:4K smpl:230K ep:587 epch:11.74 loss:0.019 grdn:0.270 lr:9.7e-05 updt_s:0.354 data_s:0.398
WARNING 2025-08-26 22:52:35 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:52:35 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:55:09 ts/train.py:232 step:4K smpl:243K ep:619 epch:12.39 loss:0.018 grdn:0.254 lr:9.6e-05 updt_s:0.352 data_s:0.412
WARNING 2025-08-26 22:55:09 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:55:09 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 22:57:43 ts/train.py:232 step:4K smpl:256K ep:652 epch:13.04 loss:0.017 grdn:0.253 lr:9.6e-05 updt_s:0.353 data_s:0.417
WARNING 2025-08-26 22:57:43 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 22:57:43 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:00:03 ts/train.py:232 step:4K smpl:269K ep:685 epch:13.69 loss:0.017 grdn:0.258 lr:9.6e-05 updt_s:0.356 data_s:0.343
WARNING 2025-08-26 23:00:03 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:00:03 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:02:27 ts/train.py:232 step:4K smpl:282K ep:717 epch:14.34 loss:0.017 grdn:0.247 lr:9.5e-05 updt_s:0.352 data_s:0.362
WARNING 2025-08-26 23:02:27 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:02:27 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:04:48 ts/train.py:232 step:5K smpl:294K ep:750 epch:15.00 loss:0.017 grdn:0.249 lr:9.5e-05 updt_s:0.354 data_s:0.348
WARNING 2025-08-26 23:04:48 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:04:48 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:07:11 ts/train.py:232 step:5K smpl:307K ep:782 epch:15.65 loss:0.016 grdn:0.247 lr:9.4e-05 updt_s:0.353 data_s:0.360
WARNING 2025-08-26 23:07:11 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:07:11 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:09:33 ts/train.py:232 step:5K smpl:320K ep:815 epch:16.30 loss:0.016 grdn:0.252 lr:9.4e-05 updt_s:0.355 data_s:0.354
WARNING 2025-08-26 23:09:33 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:09:33 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:11:53 ts/train.py:232 step:5K smpl:333K ep:848 epch:16.95 loss:0.016 grdn:0.232 lr:9.3e-05 updt_s:0.355 data_s:0.343
WARNING 2025-08-26 23:11:53 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:11:53 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:14:25 ts/train.py:232 step:5K smpl:346K ep:880 epch:17.60 loss:0.015 grdn:0.240 lr:9.3e-05 updt_s:0.353 data_s:0.407
WARNING 2025-08-26 23:14:25 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:14:25 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:16:55 ts/train.py:232 step:6K smpl:358K ep:913 epch:18.26 loss:0.015 grdn:0.229 lr:9.2e-05 updt_s:0.354 data_s:0.394
WARNING 2025-08-26 23:16:55 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:16:55 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:19:18 ts/train.py:232 step:6K smpl:371K ep:945 epch:18.91 loss:0.014 grdn:0.232 lr:9.2e-05 updt_s:0.353 data_s:0.356
WARNING 2025-08-26 23:19:18 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:19:18 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:21:39 ts/train.py:232 step:6K smpl:384K ep:978 epch:19.56 loss:0.014 grdn:0.219 lr:9.1e-05 updt_s:0.353 data_s:0.352
WARNING 2025-08-26 23:21:39 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:21:39 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:24:00 ts/train.py:232 step:6K smpl:397K ep:1K epch:20.21 loss:0.014 grdn:0.213 lr:9.0e-05 updt_s:0.356 data_s:0.346
WARNING 2025-08-26 23:24:00 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:24:00 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:26:21 ts/train.py:232 step:6K smpl:410K ep:1K epch:20.86 loss:0.014 grdn:0.213 lr:9.0e-05 updt_s:0.355 data_s:0.348
WARNING 2025-08-26 23:26:21 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:26:21 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:28:52 ts/train.py:232 step:7K smpl:422K ep:1K epch:21.52 loss:0.014 grdn:0.223 lr:8.9e-05 updt_s:0.354 data_s:0.397
WARNING 2025-08-26 23:28:52 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:28:52 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:31:24 ts/train.py:232 step:7K smpl:435K ep:1K epch:22.17 loss:0.013 grdn:0.208 lr:8.8e-05 updt_s:0.355 data_s:0.403
WARNING 2025-08-26 23:31:24 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:31:24 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:33:55 ts/train.py:232 step:7K smpl:448K ep:1K epch:22.82 loss:0.013 grdn:0.218 lr:8.8e-05 updt_s:0.356 data_s:0.396
WARNING 2025-08-26 23:33:55 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:33:55 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:36:21 ts/train.py:232 step:7K smpl:461K ep:1K epch:23.47 loss:0.013 grdn:0.216 lr:8.7e-05 updt_s:0.353 data_s:0.373
WARNING 2025-08-26 23:36:21 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:36:21 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:38:46 ts/train.py:232 step:7K smpl:474K ep:1K epch:24.13 loss:0.012 grdn:0.200 lr:8.6e-05 updt_s:0.353 data_s:0.374
WARNING 2025-08-26 23:38:46 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:38:46 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:41:17 ts/train.py:232 step:8K smpl:486K ep:1K epch:24.78 loss:0.012 grdn:0.203 lr:8.6e-05 updt_s:0.354 data_s:0.398
WARNING 2025-08-26 23:41:18 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:41:18 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:43:46 ts/train.py:232 step:8K smpl:499K ep:1K epch:25.43 loss:0.012 grdn:0.204 lr:8.5e-05 updt_s:0.353 data_s:0.386
WARNING 2025-08-26 23:43:46 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:43:46 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:46:12 ts/train.py:232 step:8K smpl:512K ep:1K epch:26.08 loss:0.012 grdn:0.197 lr:8.4e-05 updt_s:0.354 data_s:0.375
WARNING 2025-08-26 23:46:12 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:46:12 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:48:41 ts/train.py:232 step:8K smpl:525K ep:1K epch:26.73 loss:0.012 grdn:0.199 lr:8.3e-05 updt_s:0.356 data_s:0.387
WARNING 2025-08-26 23:48:41 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:48:41 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:51:15 ts/train.py:232 step:8K smpl:538K ep:1K epch:27.39 loss:0.011 grdn:0.197 lr:8.3e-05 updt_s:0.354 data_s:0.412
WARNING 2025-08-26 23:51:15 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:51:15 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:53:48 ts/train.py:232 step:9K smpl:550K ep:1K epch:28.04 loss:0.011 grdn:0.193 lr:8.2e-05 updt_s:0.355 data_s:0.408
WARNING 2025-08-26 23:53:48 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:53:48 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:56:16 ts/train.py:232 step:9K smpl:563K ep:1K epch:28.69 loss:0.011 grdn:0.193 lr:8.1e-05 updt_s:0.355 data_s:0.381
WARNING 2025-08-26 23:56:16 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:56:16 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-26 23:58:42 ts/train.py:232 step:9K smpl:576K ep:1K epch:29.34 loss:0.011 grdn:0.206 lr:8.0e-05 updt_s:0.353 data_s:0.374
WARNING 2025-08-26 23:58:42 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-26 23:58:42 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:01:02 ts/train.py:232 step:9K smpl:589K ep:1K epch:29.99 loss:0.011 grdn:0.185 lr:7.9e-05 updt_s:0.354 data_s:0.348
WARNING 2025-08-27 00:01:02 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:01:02 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:03:25 ts/train.py:232 step:9K smpl:602K ep:2K epch:30.65 loss:0.011 grdn:0.185 lr:7.9e-05 updt_s:0.353 data_s:0.356
WARNING 2025-08-27 00:03:25 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:03:25 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:05:47 ts/train.py:232 step:10K smpl:614K ep:2K epch:31.30 loss:0.011 grdn:0.189 lr:7.8e-05 updt_s:0.352 data_s:0.356
WARNING 2025-08-27 00:05:47 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:05:47 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:08:07 ts/train.py:232 step:10K smpl:627K ep:2K epch:31.95 loss:0.011 grdn:0.186 lr:7.7e-05 updt_s:0.355 data_s:0.345
WARNING 2025-08-27 00:08:07 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:08:07 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:10:39 ts/train.py:232 step:10K smpl:640K ep:2K epch:32.60 loss:0.010 grdn:0.179 lr:7.6e-05 updt_s:0.355 data_s:0.402
WARNING 2025-08-27 00:10:39 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:10:39 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:13:12 ts/train.py:232 step:10K smpl:653K ep:2K epch:33.25 loss:0.010 grdn:0.178 lr:7.5e-05 updt_s:0.354 data_s:0.410
WARNING 2025-08-27 00:13:12 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:13:12 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:15:42 ts/train.py:232 step:10K smpl:666K ep:2K epch:33.91 loss:0.010 grdn:0.173 lr:7.4e-05 updt_s:0.356 data_s:0.390
WARNING 2025-08-27 00:15:42 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:15:42 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:18:15 ts/train.py:232 step:11K smpl:678K ep:2K epch:34.56 loss:0.010 grdn:0.183 lr:7.3e-05 updt_s:0.353 data_s:0.408
WARNING 2025-08-27 00:18:15 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:18:15 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:20:45 ts/train.py:232 step:11K smpl:691K ep:2K epch:35.21 loss:0.010 grdn:0.178 lr:7.2e-05 updt_s:0.353 data_s:0.395
WARNING 2025-08-27 00:20:45 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:20:45 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:23:06 ts/train.py:232 step:11K smpl:704K ep:2K epch:35.86 loss:0.010 grdn:0.175 lr:7.2e-05 updt_s:0.355 data_s:0.346
WARNING 2025-08-27 00:23:06 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:23:06 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:25:37 ts/train.py:232 step:11K smpl:717K ep:2K epch:36.51 loss:0.009 grdn:0.179 lr:7.1e-05 updt_s:0.351 data_s:0.401
WARNING 2025-08-27 00:25:37 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:25:37 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:28:10 ts/train.py:232 step:11K smpl:730K ep:2K epch:37.17 loss:0.009 grdn:0.173 lr:7.0e-05 updt_s:0.353 data_s:0.412
WARNING 2025-08-27 00:28:10 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:28:10 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:30:39 ts/train.py:232 step:12K smpl:742K ep:2K epch:37.82 loss:0.009 grdn:0.158 lr:6.9e-05 updt_s:0.354 data_s:0.387
WARNING 2025-08-27 00:30:39 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:30:39 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:33:07 ts/train.py:232 step:12K smpl:755K ep:2K epch:38.47 loss:0.009 grdn:0.167 lr:6.8e-05 updt_s:0.351 data_s:0.387
WARNING 2025-08-27 00:33:07 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:33:07 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:35:30 ts/train.py:232 step:12K smpl:768K ep:2K epch:39.12 loss:0.009 grdn:0.164 lr:6.7e-05 updt_s:0.353 data_s:0.362
WARNING 2025-08-27 00:35:30 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:35:30 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:38:01 ts/train.py:232 step:12K smpl:781K ep:2K epch:39.77 loss:0.009 grdn:0.167 lr:6.6e-05 updt_s:0.353 data_s:0.397
WARNING 2025-08-27 00:38:01 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:38:01 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:40:28 ts/train.py:232 step:12K smpl:794K ep:2K epch:40.43 loss:0.009 grdn:0.162 lr:6.5e-05 updt_s:0.354 data_s:0.380
WARNING 2025-08-27 00:40:28 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:40:28 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:42:52 ts/train.py:232 step:13K smpl:806K ep:2K epch:41.08 loss:0.009 grdn:0.164 lr:6.4e-05 updt_s:0.354 data_s:0.360
WARNING 2025-08-27 00:42:52 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:42:52 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:45:20 ts/train.py:232 step:13K smpl:819K ep:2K epch:41.73 loss:0.009 grdn:0.160 lr:6.3e-05 updt_s:0.353 data_s:0.387
WARNING 2025-08-27 00:45:20 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:45:20 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:47:48 ts/train.py:232 step:13K smpl:832K ep:2K epch:42.38 loss:0.008 grdn:0.157 lr:6.2e-05 updt_s:0.354 data_s:0.382
WARNING 2025-08-27 00:47:48 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:47:48 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:50:04 ts/train.py:232 step:13K smpl:845K ep:2K epch:43.03 loss:0.008 grdn:0.157 lr:6.1e-05 updt_s:0.354 data_s:0.325
WARNING 2025-08-27 00:50:04 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:50:04 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:52:38 ts/train.py:232 step:13K smpl:858K ep:2K epch:43.69 loss:0.008 grdn:0.157 lr:6.0e-05 updt_s:0.353 data_s:0.412
WARNING 2025-08-27 00:52:38 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:52:38 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:55:07 ts/train.py:232 step:14K smpl:870K ep:2K epch:44.34 loss:0.008 grdn:0.147 lr:5.9e-05 updt_s:0.353 data_s:0.390
WARNING 2025-08-27 00:55:07 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:55:07 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 00:57:27 ts/train.py:232 step:14K smpl:883K ep:2K epch:44.99 loss:0.008 grdn:0.155 lr:5.8e-05 updt_s:0.354 data_s:0.345
WARNING 2025-08-27 00:57:27 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 00:57:27 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:00:00 ts/train.py:232 step:14K smpl:896K ep:2K epch:45.64 loss:0.008 grdn:0.156 lr:5.7e-05 updt_s:0.353 data_s:0.408
WARNING 2025-08-27 01:00:00 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:00:00 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:02:27 ts/train.py:232 step:14K smpl:909K ep:2K epch:46.29 loss:0.008 grdn:0.152 lr:5.6e-05 updt_s:0.355 data_s:0.376
WARNING 2025-08-27 01:02:27 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:02:27 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:04:48 ts/train.py:232 step:14K smpl:922K ep:2K epch:46.95 loss:0.008 grdn:0.150 lr:5.5e-05 updt_s:0.355 data_s:0.349
WARNING 2025-08-27 01:04:48 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:04:48 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:07:21 ts/train.py:232 step:15K smpl:934K ep:2K epch:47.60 loss:0.008 grdn:0.147 lr:5.4e-05 updt_s:0.354 data_s:0.408
WARNING 2025-08-27 01:07:21 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:07:21 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:09:50 ts/train.py:232 step:15K smpl:947K ep:2K epch:48.25 loss:0.008 grdn:0.149 lr:5.3e-05 updt_s:0.352 data_s:0.394
WARNING 2025-08-27 01:09:50 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:09:50 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:12:12 ts/train.py:232 step:15K smpl:960K ep:2K epch:48.90 loss:0.007 grdn:0.148 lr:5.2e-05 updt_s:0.354 data_s:0.351
WARNING 2025-08-27 01:12:12 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:12:12 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:14:42 ts/train.py:232 step:15K smpl:973K ep:2K epch:49.55 loss:0.007 grdn:0.144 lr:5.1e-05 updt_s:0.354 data_s:0.394
WARNING 2025-08-27 01:14:42 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:14:42 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:17:13 ts/train.py:232 step:15K smpl:986K ep:3K epch:50.21 loss:0.007 grdn:0.141 lr:5.0e-05 updt_s:0.354 data_s:0.400
WARNING 2025-08-27 01:17:13 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:17:13 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:19:34 ts/train.py:232 step:16K smpl:998K ep:3K epch:50.86 loss:0.007 grdn:0.139 lr:4.9e-05 updt_s:0.355 data_s:0.346
WARNING 2025-08-27 01:19:34 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:19:34 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:21:58 ts/train.py:232 step:16K smpl:1M ep:3K epch:51.51 loss:0.007 grdn:0.143 lr:4.8e-05 updt_s:0.354 data_s:0.365
WARNING 2025-08-27 01:21:58 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:21:58 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:24:22 ts/train.py:232 step:16K smpl:1M ep:3K epch:52.16 loss:0.007 grdn:0.141 lr:4.7e-05 updt_s:0.354 data_s:0.365
WARNING 2025-08-27 01:24:22 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:24:22 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:26:46 ts/train.py:232 step:16K smpl:1M ep:3K epch:52.81 loss:0.007 grdn:0.141 lr:4.6e-05 updt_s:0.354 data_s:0.363
WARNING 2025-08-27 01:26:46 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:26:46 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:29:09 ts/train.py:232 step:16K smpl:1M ep:3K epch:53.47 loss:0.006 grdn:0.135 lr:4.5e-05 updt_s:0.354 data_s:0.357
WARNING 2025-08-27 01:29:09 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:29:09 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:31:34 ts/train.py:232 step:17K smpl:1M ep:3K epch:54.12 loss:0.006 grdn:0.128 lr:4.4e-05 updt_s:0.355 data_s:0.371
WARNING 2025-08-27 01:31:34 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:31:34 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:34:05 ts/train.py:232 step:17K smpl:1M ep:3K epch:54.77 loss:0.007 grdn:0.135 lr:4.3e-05 updt_s:0.353 data_s:0.398
WARNING 2025-08-27 01:34:05 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:34:05 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:36:33 ts/train.py:232 step:17K smpl:1M ep:3K epch:55.42 loss:0.006 grdn:0.132 lr:4.2e-05 updt_s:0.354 data_s:0.384
WARNING 2025-08-27 01:36:33 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:36:33 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:38:58 ts/train.py:232 step:17K smpl:1M ep:3K epch:56.07 loss:0.006 grdn:0.133 lr:4.1e-05 updt_s:0.355 data_s:0.366
WARNING 2025-08-27 01:38:58 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:38:58 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:41:19 ts/train.py:232 step:17K smpl:1M ep:3K epch:56.73 loss:0.006 grdn:0.124 lr:4.0e-05 updt_s:0.356 data_s:0.346
WARNING 2025-08-27 01:41:19 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:41:19 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:43:49 ts/train.py:232 step:18K smpl:1M ep:3K epch:57.38 loss:0.006 grdn:0.128 lr:3.9e-05 updt_s:0.354 data_s:0.396
WARNING 2025-08-27 01:43:49 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:43:49 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:46:18 ts/train.py:232 step:18K smpl:1M ep:3K epch:58.03 loss:0.006 grdn:0.127 lr:3.8e-05 updt_s:0.356 data_s:0.387
WARNING 2025-08-27 01:46:18 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:46:18 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:48:41 ts/train.py:232 step:18K smpl:1M ep:3K epch:58.68 loss:0.006 grdn:0.120 lr:3.7e-05 updt_s:0.355 data_s:0.358
WARNING 2025-08-27 01:48:41 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:48:41 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:51:03 ts/train.py:232 step:18K smpl:1M ep:3K epch:59.33 loss:0.006 grdn:0.122 lr:3.6e-05 updt_s:0.353 data_s:0.351
WARNING 2025-08-27 01:51:03 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:51:03 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:53:23 ts/train.py:232 step:18K smpl:1M ep:3K epch:59.99 loss:0.006 grdn:0.124 lr:3.5e-05 updt_s:0.354 data_s:0.344
WARNING 2025-08-27 01:53:23 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:53:23 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:55:46 ts/train.py:232 step:19K smpl:1M ep:3K epch:60.64 loss:0.006 grdn:0.124 lr:3.4e-05 updt_s:0.351 data_s:0.363
WARNING 2025-08-27 01:55:46 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:55:46 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 01:58:11 ts/train.py:232 step:19K smpl:1M ep:3K epch:61.29 loss:0.006 grdn:0.121 lr:3.3e-05 updt_s:0.353 data_s:0.368
WARNING 2025-08-27 01:58:11 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 01:58:11 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:00:32 ts/train.py:232 step:19K smpl:1M ep:3K epch:61.94 loss:0.006 grdn:0.122 lr:3.2e-05 updt_s:0.352 data_s:0.352
WARNING 2025-08-27 02:00:32 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:00:32 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:02:55 ts/train.py:232 step:19K smpl:1M ep:3K epch:62.59 loss:0.006 grdn:0.116 lr:3.1e-05 updt_s:0.354 data_s:0.358
WARNING 2025-08-27 02:02:55 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:02:55 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:05:21 ts/train.py:232 step:19K smpl:1M ep:3K epch:63.25 loss:0.006 grdn:0.119 lr:3.0e-05 updt_s:0.351 data_s:0.376
WARNING 2025-08-27 02:05:21 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:05:21 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:07:50 ts/train.py:232 step:20K smpl:1M ep:3K epch:63.90 loss:0.005 grdn:0.117 lr:2.9e-05 updt_s:0.352 data_s:0.394
WARNING 2025-08-27 02:07:50 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:07:50 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:10:15 ts/train.py:232 step:20K smpl:1M ep:3K epch:64.55 loss:0.005 grdn:0.114 lr:2.8e-05 updt_s:0.353 data_s:0.368
WARNING 2025-08-27 02:10:15 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:10:15 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:12:40 ts/train.py:232 step:20K smpl:1M ep:3K epch:65.20 loss:0.006 grdn:0.116 lr:2.7e-05 updt_s:0.353 data_s:0.368
WARNING 2025-08-27 02:12:40 db_utils.py:141 WandB logging of key "losses_after_forward" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
WARNING 2025-08-27 02:12:40 db_utils.py:141 WandB logging of key "losses_after_rm_padding" was ignored as its type "<class 'torch.Tensor'>" is not handled by this wrapper.
INFO 2025-08-27 02:12:40 ts/train.py:241 Checkpoint policy after step 20000
INFO 2025-08-27 02:12:52 ts/train.py:283 End of training
