2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_setup.py:_flush():80] Current SDK version is 0.21.1
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_setup.py:_flush():80] Configure stats pid to 2135310
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/myrobot/wandb/settings
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_init.py:setup_run_log_directory():703] Logging user logs to outputs/train/my_smolvla/wandb/run-20250826_220825-2fhb9rwp/logs/debug.log
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to outputs/train/my_smolvla/wandb/run-20250826_220825-2fhb9rwp/logs/debug-internal.log
2025-08-26 22:08:25,656 INFO    MainThread:2135310 [wandb_init.py:init():830] calling init triggers
2025-08-26 22:08:25,657 INFO    MainThread:2135310 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'dataset': {'repo_id': 'lerobot/svla_so100_pickplace', 'root': None, 'episodes': None, 'image_transforms': {'enable': False, 'max_num_transforms': 3, 'random_order': False, 'tfs': {'brightness': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'brightness': [0.8, 1.2]}}, 'contrast': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'contrast': [0.8, 1.2]}}, 'saturation': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'saturation': [0.5, 1.5]}}, 'hue': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'hue': [-0.05, 0.05]}}, 'sharpness': {'weight': 1.0, 'type': 'SharpnessJitter', 'kwargs': {'sharpness': [0.5, 1.5]}}}}, 'revision': None, 'use_imagenet_stats': True, 'video_backend': 'torchcodec'}, 'env': None, 'policy': {'type': 'smolvla', 'n_obs_steps': 1, 'normalization_mapping': {'VISUAL': <NormalizationMode.IDENTITY: 'IDENTITY'>, 'STATE': <NormalizationMode.MEAN_STD: 'MEAN_STD'>, 'ACTION': <NormalizationMode.MEAN_STD: 'MEAN_STD'>}, 'input_features': {'observation.state': {'type': <FeatureType.STATE: 'STATE'>, 'shape': [6]}, 'observation.image2': {'type': <FeatureType.VISUAL: 'VISUAL'>, 'shape': [3, 256, 256]}, 'observation.image': {'type': <FeatureType.VISUAL: 'VISUAL'>, 'shape': [3, 256, 256]}, 'observation.image3': {'type': <FeatureType.VISUAL: 'VISUAL'>, 'shape': [3, 256, 256]}}, 'output_features': {'action': {'type': <FeatureType.ACTION: 'ACTION'>, 'shape': [6]}}, 'device': 'cuda', 'use_amp': False, 'push_to_hub': False, 'repo_id': None, 'private': None, 'tags': None, 'license': None, 'chunk_size': 50, 'n_action_steps': 50, 'max_state_dim': 32, 'max_action_dim': 32, 'resize_imgs_with_padding': [512, 512], 'empty_cameras': 0, 'adapt_to_pi_aloha': False, 'use_delta_joint_actions_aloha': False, 'tokenizer_max_length': 48, 'num_steps': 10, 'use_cache': True, 'freeze_vision_encoder': True, 'train_expert_only': True, 'train_state_proj': True, 'optimizer_lr': 0.0001, 'optimizer_betas': [0.9, 0.95], 'optimizer_eps': 1e-08, 'optimizer_weight_decay': 1e-10, 'optimizer_grad_clip_norm': 10.0, 'scheduler_warmup_steps': 1000, 'scheduler_decay_steps': 30000, 'scheduler_decay_lr': 2.5e-06, 'vlm_model_name': 'HuggingFaceTB/SmolVLM2-500M-Video-Instruct', 'load_vlm_weights': True, 'add_image_special_tokens': False, 'attention_mode': 'cross_attn', 'prefix_length': 0, 'pad_language_to': 'max_length', 'num_expert_layers': 0, 'num_vlm_layers': 16, 'self_attn_every_n_layers': 2, 'expert_width_multiplier': 0.75, 'min_period': 0.004, 'max_period': 4.0}, 'output_dir': 'outputs/train/my_smolvla', 'job_name': 'my_smolvla_training', 'resume': False, 'seed': 1000, 'num_workers': 4, 'batch_size': 64, 'steps': 20000, 'eval_freq': 20000, 'log_freq': 200, 'save_checkpoint': True, 'save_freq': 20000, 'use_policy_training_preset': True, 'optimizer': {'type': 'adamw', 'lr': 0.0001, 'weight_decay': 1e-10, 'grad_clip_norm': 10.0, 'betas': [0.9, 0.95], 'eps': 1e-08}, 'scheduler': {'type': 'cosine_decay_with_warmup', 'num_warmup_steps': 1000, 'num_decay_steps': 30000, 'peak_lr': 0.0001, 'decay_lr': 2.5e-06}, 'eval': {'n_episodes': 50, 'batch_size': 50, 'use_async_envs': False}, 'wandb': {'enable': True, 'disable_artifact': False, 'project': 'lerobot', 'entity': None, 'notes': None, 'run_id': None, 'mode': None}, '_wandb': {}}
2025-08-26 22:08:25,657 INFO    MainThread:2135310 [wandb_init.py:init():871] starting backend
2025-08-26 22:08:25,870 INFO    MainThread:2135310 [wandb_init.py:init():874] sending inform_init request
2025-08-26 22:08:25,874 INFO    MainThread:2135310 [wandb_init.py:init():882] backend started and connected
2025-08-26 22:08:25,878 INFO    MainThread:2135310 [wandb_init.py:init():953] updated telemetry
2025-08-26 22:08:25,879 INFO    MainThread:2135310 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-26 22:08:26,972 INFO    MainThread:2135310 [wandb_init.py:init():1029] starting run threads in backend
2025-08-26 22:08:27,326 INFO    MainThread:2135310 [wandb_run.py:_console_start():2494] atexit reg
2025-08-26 22:08:27,326 INFO    MainThread:2135310 [wandb_run.py:_redirect():2342] redirect: wrap_raw
2025-08-26 22:08:27,327 INFO    MainThread:2135310 [wandb_run.py:_redirect():2411] Wrapping output streams.
2025-08-26 22:08:27,327 INFO    MainThread:2135310 [wandb_run.py:_redirect():2434] Redirects installed.
2025-08-26 22:08:27,330 INFO    MainThread:2135310 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-27 02:12:53,643 INFO    MsgRouterThr:2135310 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.
