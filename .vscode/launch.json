{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "inputs": [
        {
            "id": "sourceRoot",
            "description": "Source root directory",
            "default": "lerobot",
            "type": "promptString"
        }
    ],
    "configurations": [
        {
            "name": "Python Debugger: Current File with Arguments",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "args": "${command:pickArgs}",
            "cwd": "${workspaceFolder}/${input:sourceRoot}"
        },
        {
            "name": "Python: Attach",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "localhost",
                "port": 9501
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}/${input:sourceRoot}",
                    "remoteRoot": "."
                }
            ]
        }
    ]
}
