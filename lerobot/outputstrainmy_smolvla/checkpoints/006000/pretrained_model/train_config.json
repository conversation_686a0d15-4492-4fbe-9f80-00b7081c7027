{"dataset": {"repo_id": "xxll/smolvla-record", "root": null, "episodes": null, "image_transforms": {"enable": false, "max_num_transforms": 3, "random_order": false, "tfs": {"brightness": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"brightness": [0.8, 1.2]}}, "contrast": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"contrast": [0.8, 1.2]}}, "saturation": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"saturation": [0.5, 1.5]}}, "hue": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"hue": [-0.05, 0.05]}}, "sharpness": {"weight": 1.0, "type": "SharpnessJitter", "kwargs": {"sharpness": [0.5, 1.5]}}}}, "revision": null, "use_imagenet_stats": true, "video_backend": "torchcodec"}, "env": null, "policy": {"type": "smolvla", "n_obs_steps": 1, "normalization_mapping": {"VISUAL": "IDENTITY", "STATE": "MEAN_STD", "ACTION": "MEAN_STD"}, "input_features": {"observation.state": {"type": "STATE", "shape": [6]}, "observation.images.up": {"type": "VISUAL", "shape": [3, 480, 640]}, "observation.images.front": {"type": "VISUAL", "shape": [3, 480, 640]}}, "output_features": {"action": {"type": "ACTION", "shape": [6]}}, "device": "cuda", "use_amp": false, "push_to_hub": false, "repo_id": null, "private": null, "tags": null, "license": null, "chunk_size": 50, "n_action_steps": 50, "max_state_dim": 32, "max_action_dim": 32, "resize_imgs_with_padding": [512, 512], "empty_cameras": 0, "adapt_to_pi_aloha": false, "use_delta_joint_actions_aloha": false, "tokenizer_max_length": 48, "num_steps": 10, "use_cache": true, "freeze_vision_encoder": true, "train_expert_only": true, "train_state_proj": true, "optimizer_lr": 0.0001, "optimizer_betas": [0.9, 0.95], "optimizer_eps": 1e-08, "optimizer_weight_decay": 1e-10, "optimizer_grad_clip_norm": 10.0, "scheduler_warmup_steps": 1000, "scheduler_decay_steps": 30000, "scheduler_decay_lr": 2.5e-06, "vlm_model_name": "HuggingFaceTB/SmolVLM2-500M-Video-Instruct", "load_vlm_weights": true, "add_image_special_tokens": false, "attention_mode": "cross_attn", "prefix_length": 0, "pad_language_to": "max_length", "num_expert_layers": 0, "num_vlm_layers": 16, "self_attn_every_n_layers": 2, "expert_width_multiplier": 0.75, "min_period": 0.004, "max_period": 4.0}, "output_dir": "outputstrainmy_smolvla", "job_name": "my_smolvla_training", "resume": false, "seed": 1000, "num_workers": 4, "batch_size": 64, "steps": 20000, "eval_freq": 20000, "log_freq": 200, "save_checkpoint": true, "save_freq": 500, "use_policy_training_preset": true, "optimizer": {"type": "adamw", "lr": 0.0001, "weight_decay": 1e-10, "grad_clip_norm": 10.0, "betas": [0.9, 0.95], "eps": 1e-08}, "scheduler": {"type": "cosine_decay_with_warmup", "num_warmup_steps": 1000, "num_decay_steps": 30000, "peak_lr": 0.0001, "decay_lr": 2.5e-06}, "eval": {"n_episodes": 50, "batch_size": 50, "use_async_envs": false}, "wandb": {"enable": true, "disable_artifact": false, "project": "<PERSON><PERSON><PERSON>", "entity": null, "notes": null, "run_id": "5txppn5y", "mode": null}}