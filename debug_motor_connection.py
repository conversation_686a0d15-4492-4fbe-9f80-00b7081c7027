#!/usr/bin/env python3
"""
Debug script for motor connection issues.
Use this to diagnose "no status packet" errors before enabling torque.
"""

import logging
from lerobot.motors.dynamixel import DynamixelMotorsBus

# Enable debug logging to see communication details
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def diagnose_motor_connection(bus, motor_id_or_name):
    """Diagnose connection issues with a specific motor."""
    
    print(f"\n=== Diagnosing Motor: {motor_id_or_name} ===")
    
    # 1. Check if bus is connected
    if not bus.is_connected:
        print("❌ Bus is not connected. Connecting...")
        try:
            bus.connect(handshake=False)  # Skip handshake to avoid initial failures
            print("✅ Bus connected successfully")
        except Exception as e:
            print(f"❌ Failed to connect bus: {e}")
            return False
    
    # 2. Try to ping the motor
    print(f"🔍 Pinging motor {motor_id_or_name}...")
    try:
        model_number = bus.ping(motor_id_or_name, num_retry=3, raise_on_error=False)
        if model_number is not None:
            print(f"✅ Motor responds! Model number: {model_number}")
        else:
            print("❌ Motor does not respond to ping")
            return False
    except Exception as e:
        print(f"❌ Ping failed with exception: {e}")
        return False
    
    # 3. Try to read current torque status
    print("🔍 Reading current torque enable status...")
    try:
        current_torque = bus.read("Torque_Enable", motor_id_or_name, num_retry=3)
        print(f"✅ Current torque status: {current_torque}")
    except Exception as e:
        print(f"❌ Failed to read torque status: {e}")
        return False
    
    # 4. Try to read present position (basic communication test)
    print("🔍 Reading present position...")
    try:
        position = bus.read("Present_Position", motor_id_or_name, num_retry=3)
        print(f"✅ Present position: {position}")
    except Exception as e:
        print(f"❌ Failed to read present position: {e}")
        return False
    
    return True

def safe_enable_torque(bus, motor_id_or_name):
    """Safely enable torque with proper error handling and retries."""
    
    print(f"\n=== Safely Enabling Torque for Motor: {motor_id_or_name} ===")
    
    # First diagnose the connection
    if not diagnose_motor_connection(bus, motor_id_or_name):
        print("❌ Motor diagnosis failed. Cannot safely enable torque.")
        return False
    
    # Try to enable torque with retries
    print("🔧 Attempting to enable torque...")
    try:
        # Use the write method with retries instead of enable_torque for better control
        bus.write("Torque_Enable", motor_id_or_name, 1, num_retry=5)
        print("✅ Torque enabled successfully!")
        
        # Verify torque was actually enabled
        torque_status = bus.read("Torque_Enable", motor_id_or_name)
        if torque_status == 1:
            print("✅ Torque enable verified!")
            return True
        else:
            print(f"⚠️  Torque enable command sent but status is still: {torque_status}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to enable torque: {e}")
        return False

def scan_all_motors(bus):
    """Scan for all responsive motors on the bus."""
    print("\n=== Scanning for all motors on bus ===")
    
    if not bus.is_connected:
        bus.connect(handshake=False)
    
    responsive_motors = []
    
    # Try IDs 1-20 (common range)
    for motor_id in range(1, 21):
        try:
            model_number = bus.ping(motor_id, num_retry=1, raise_on_error=False)
            if model_number is not None:
                print(f"✅ Found motor at ID {motor_id}, model: {model_number}")
                responsive_motors.append((motor_id, model_number))
        except:
            pass
    
    if not responsive_motors:
        print("❌ No responsive motors found")
    else:
        print(f"✅ Found {len(responsive_motors)} responsive motors: {responsive_motors}")
    
    return responsive_motors

# Example usage:
if __name__ == "__main__":
    # Replace with your actual bus configuration
    # bus = DynamixelMotorsBus(port="/dev/ttyUSB0", motors={"motor_6": (6, "your_model")})
    
    print("Motor Connection Diagnostic Tool")
    print("================================")
    print("1. First run scan_all_motors(bus) to find all responsive motors")
    print("2. Then run diagnose_motor_connection(bus, 6) for the problematic motor")
    print("3. Finally run safe_enable_torque(bus, 6) to safely enable torque")
    print("\nMake sure to replace the bus configuration with your actual setup!")
